import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';

// GET /api/360s/[id] - Get single 360 (no authentication required)
export async function GET(request, { params }) {
  try {
    console.log('GET request received for 360');

    await connectDB();
    console.log('Database connected for GET request');

    const { id } = await params;
    console.log('Fetching 360 with ID:', id);

    // Validate ID format
    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid 360 ID format',
        },
        { status: 400 }
      );
    }

    const item = await _360Settings.findById(id);

    if (!item) {
      console.log('No 360 found with ID:', id);
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }

    console.log('360 found successfully');
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching 360:', error);
    console.error('Error stack:', error.stack);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// Shared update logic for both PUT and PATCH methods
const handleUpdate = async (request, { params }) => {
  try {
    console.log('360° Update Request Started');

    // Connect to database with timeout
    const dbConnection = await Promise.race([
      connectDB(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database connection timeout')), 10000)
      )
    ]);

    console.log('Database connected successfully');

    const { id } = await params;
    console.log('Processing update for ID:', id);

    // Validate ID format
    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid 360 ID format',
        },
        { status: 400 }
      );
    }

    const body = await request.json();
    console.log('Request body received:', Object.keys(body));

    // Validate and log URL format if present
    if (body.url) {
      const isFirebaseURL = body.url.includes('firebasestorage.googleapis.com');
      const isLocalURL = body.url.startsWith('/uploads/');

      console.log('URL validation:', {
        url: body.url.substring(0, 100) + '...',
        isFirebaseURL,
        isLocalURL,
        urlLength: body.url.length
      });

      if (isLocalURL) {
        console.warn('WARNING: Local URL detected in update request. This should be a Firebase URL!');
      }

      if (isFirebaseURL) {
        console.log('✅ Firebase URL detected - this is correct');
      }
    }

    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;

    // Validate URL if it's being updated
    if (body.url) {
      const url = String(body.url).trim();
      if (!url.startsWith('https://firebasestorage.googleapis.com/')) {
        console.error(`Validation failed: URL must be a Firebase Storage URL, got: ${url}`);
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid URL',
            message: 'URL must be a Firebase Storage URL. Local upload URLs are not allowed for 360° images.',
            receivedUrl: url,
            expectedFormat: 'https://firebasestorage.googleapis.com/...',
          },
          { status: 400 }
        );
      }
      body.url = url; // Use the trimmed URL
    }

    // Validate and clean marker data if present
    if (body.markerList && Array.isArray(body.markerList)) {
      console.log('Processing marker list with', body.markerList.length, 'markers');
      body.markerList = body.markerList.map(marker => ({
        name: marker.name || '',
        markerType: marker.markerType || '',
        x: Number(marker.x) || 0,
        y: Number(marker.y) || 0,
        z: Number(marker.z) || 0,
        _360Name: marker._360Name || '',
        id: marker.id || '',
        // Include new infoVideo marker properties
        ...(marker.markerType === 'infoVideo' ? {
          videoName: marker.videoName || '',
          infoVideo: marker.infoVideo || null
        } : {})
      }));
    }

    console.log('Attempting database update...');

    // First check if the document exists
    const existingDoc = await _360Settings.findById(id);
    if (!existingDoc) {
      console.log('Document not found with ID:', id);
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }

    console.log('Document found:', existingDoc.name);
    console.log('Current markerList length:', existingDoc.markerList?.length || 0);

    // Perform update with timeout and proper options
    const updated360 = await Promise.race([
      _360Settings.findByIdAndUpdate(
        id,
        body, // Direct update without $set to ensure all fields are updated
        {
          new: true,
          runValidators: true, // Enable validators to ensure schema compliance
          lean: false, // Ensure we get a full document
          upsert: false, // Don't create if not exists
          strict: false // Allow fields not in schema (for flexibility)
        }
      ),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database update timeout')), 10000)
      )
    ]);

    console.log('Database update completed');

    if (!updated360) {
      console.log('No document found with ID:', id);
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }

    // Verify the update was successful by fetching the document again
    const verificationDoc = await _360Settings.findById(id);
    console.log('Verification - markerList length:', verificationDoc?.markerList?.length || 0);

    // Check if infoVideo markers were saved properly
    const infoVideoMarkers = verificationDoc?.markerList?.filter(m => m.markerType === 'infoVideo') || [];
    console.log('Verification - infoVideo markers count:', infoVideoMarkers.length);

    console.log('Update successful, returning response');

    return NextResponse.json({
      success: true,
      data: updated360,
      message: '360 updated successfully',
    });
  } catch (error) {
    console.error('Error updating 360:', error);
    console.error('Error stack:', error.stack);

    // Handle specific error types
    if (error.message.includes('timeout')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Timeout Error',
          message: 'Operation timed out. Please try again.',
        },
        { status: 408 }
      );
    }

    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error.name === 'CastError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid ID',
          message: 'Invalid 360 ID format',
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
};

// PUT /api/360s/[id] - Update 360 (complete replacement, no authentication required)
export async function PUT(request, { params }) {
  return handleUpdate(request, { params });
}

// PATCH /api/360s/[id] - Partial update 360 (partial updates, no authentication required)
export async function PATCH(request, { params }) {
  return handleUpdate(request, { params });
}

// DELETE /api/360s/[id] - Delete 360 (no authentication required)
export async function DELETE(request, { params }) {
  try {
    await connectDB();
    
    const { id } = await params;
    
    const deleted360 = await _360Settings.findByIdAndDelete(id);
    
    if (!deleted360) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: deleted360,
      message: '360 deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting 360:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
